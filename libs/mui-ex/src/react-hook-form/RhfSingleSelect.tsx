import {
  FormControl,
  FormHelperText,
  InputLabel,
  LinearProgress,
  MenuItem,
  Select,
  type SelectChangeEvent,
  type SxProps,
  type Theme,
} from '@mui/material';
import { type Option, isNullish } from 'common';
import { type FieldValues, type UseControllerProps, useController } from 'react-hook-form';
import { match } from 'ts-pattern';

type Props<T extends FieldValues> = UseControllerProps<T> & {
  options: Option[];
  label?: string;
  helperText?: string;
  disabled?: boolean;
  readOnly?: boolean;
  loading?: boolean;
  showEmptyValue?: boolean;
  onChange?: (value: string) => void;
};

export const RhfSingleSelect = <T extends FieldValues>(props: Props<T>) => {
  const {
    control,
    name,
    options,
    label,
    helperText,
    disabled,
    readOnly,
    loading,
    showEmptyValue = true,
    onChange,
  } = props;
  const {
    field: { ref, ...field },
    fieldState: { error },
  } = useController({ name, control });

  const handleChange = (event: SelectChangeEvent<string>) => {
    const value = event.target.value;
    onChange?.(value);
    field.onChange(value);
  };

  const isEmptyValue = isNullish(field.value) || field.value === '';

  return (
    <FormControl fullWidth focused={readOnly}>
      <InputLabel id={name}>{label}</InputLabel>
      <Select
        {...field}
        value={field.value ?? ''}
        inputRef={ref}
        labelId={name}
        onChange={!readOnly ? handleChange : undefined}
        error={Boolean(error)}
        disabled={disabled}
        readOnly={readOnly}
      >
        {showEmptyValue && isEmptyValue && (
          <MenuItem value="">
            <em>未選択</em>
          </MenuItem>
        )}
        {options.map((option) => {
          const highlight = match(option)
            .with({ primary: true }, () => 'primary' as const)
            .with({ secondary: true }, () => 'secondary' as const)
            .otherwise(() => undefined);
          const sx: SxProps<Theme> = (t) => ({
            ...(highlight && {
              bgcolor: `${t.palette[highlight].main}10`,
              '&.MuiMenuItem-root.Mui-selected': {
                bgcolor: `${t.palette[highlight].main}20`,
              },
              '&.MuiMenuItem-root.Mui-selected:hover': {
                bgcolor: `${t.palette[highlight].main}30`,
              },
            }),
          });
          return (
            <MenuItem key={option.value} value={option.value} sx={sx}>
              {option.label}
            </MenuItem>
          );
        })}
      </Select>
      <FormHelperText error={Boolean(error)}>{error?.message ?? helperText}</FormHelperText>
      {loading && <LinearProgress sx={{ mt: -0.75 }} />}
    </FormControl>
  );
};
