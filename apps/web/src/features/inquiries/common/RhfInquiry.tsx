import { FormHelperText, Grid } from '@mui/material';
import type { Control, UseFormWatch } from 'react-hook-form';
import { z } from 'zod';

import { type Image, ImageSchema, base64ToBlob, base64ToContentType, isLocalImage } from 'common';
import { RhfDateTimePicker, RhfSingleSelect, RhfTextField } from 'mui-ex';

import { trpc, trpcClient } from '@/api';
import { RhfSimpleAddress } from '@/components/react-hook-form/address/RhfSimpleAddress';
import { RhfMultiImageSelector } from '@/components/react-hook-form/image-selector/RhfMultiImageSelector';
import { useAppContext } from '@/contexts/app-context';
import { datetimeFormatter } from '@/funcs/date';
import zip from 'just-zip-it';
import type { Inquiry } from 'lambda-api';

export const InquiryStateSchema = z.object({
  receptionRouteId: z.string().min(1, '受付ルートを選択してください'),
  receiverId: z.string().min(1, '受付者を選択してください'),
  receivedAt: z.date().nullable(),
  title: z.string(),
  description: z.string().min(1, '問い合わせ内容を入力してください'),
  address: z.string(),
  memo: z.string(),
  images: z.array(ImageSchema),
  assignUserId: z.string().optional(),
});
export type InquiryState = z.infer<typeof InquiryStateSchema>;

// TODO: タイトルを入力できるようにする
export const generateInquiryTitle = (
  receptionRouteId: string | null,
  createdAt: Date,
  receptionRoutes: { id: string; name: string }[],
): string => {
  const receptionRoute = receptionRoutes.find((route) => route.id === receptionRouteId);
  const receptionRouteName = receptionRoute?.name || '不明な受付ルート';
  const createdDateTime = datetimeFormatter(createdAt);
  return `${receptionRouteName} (${createdDateTime})`;
};

export const createDefaultInquiryState = (currentUserId?: string): InquiryState => ({
  receptionRouteId: '',
  receiverId: currentUserId || '',
  receivedAt: new Date(),
  title: '',
  description: '',
  address: '',
  memo: '',
  images: [],
  assignUserId: undefined,
});
export const inquiryToState = (
  inquiry: Inquiry,
  receptionRoutes: { id: string; name: string }[],
): InquiryState => ({
  receptionRouteId: inquiry.receptionRouteId ?? '',
  receiverId: inquiry.receiverId ?? '',
  receivedAt: inquiry.receivedAt,
  title: generateInquiryTitle(inquiry.receptionRouteId, inquiry.createdAt, receptionRoutes),
  description: inquiry.description,
  address: inquiry?.address ?? '',
  memo: inquiry.memo ?? '',
  images: inquiry.images,
  assignUserId: inquiry.assignUsers?.[0]?.userId,
});

export const uploadInquiryImagesToS3 = async (inquiryId: string, images: Image[]) => {
  const localImages = images.filter(isLocalImage);
  const res = await trpcClient.presignedUrls.inquiry.list.query({
    inquiryId,
    files: localImages.map(({ filename, base64 }) => ({
      contentType: base64ToContentType(base64),
      filename,
    })),
  });
  const uploaded = await Promise.all(
    zip(localImages, res).map(async ([{ id, base64, description }, { key, url, fields }]) => {
      const body = new FormData();
      for (const [key, value] of Object.entries(fields)) {
        body.append(key, value);
      }
      body.append('file', base64ToBlob(base64));
      const response = await fetch(url, { method: 'POST', body });
      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${errorText}`);
      }
      return { id, key, description, base64 };
    }),
  );
  return images.map((image, sortOrder) => {
    if (isLocalImage(image)) {
      const found = uploaded.find((u) => u.base64 === image.base64);
      if (found === undefined) throw new Error('Upload failed');
      const { id, key, description } = found;
      return { id, key, description, sortOrder };
    }
    return { ...image, sortOrder };
  });
};

type CommonProps = { readOnly?: boolean };
type State = InquiryState;
type Props<T extends State> = CommonProps & {
  control: Control<T>;
  watch: UseFormWatch<T>;
};
type InnerProps = CommonProps & {
  control: Control<State>;
  watch: UseFormWatch<State>;
};

export const RhfInquiry = <T extends State>(props: Props<T>) => {
  const { control, watch, readOnly } = props as unknown as InnerProps;
  const {
    labels,
    tenant: { receptionRoutes },
  } = useAppContext();
  // おそらく受付担当者は一部のユーザーのみ
  const { data: users = [], isPending } = trpc.users.list.useQuery();
  return (
    <>
      <Grid container spacing={2}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <RhfSingleSelect
            control={control}
            name="receptionRouteId"
            label="受付ルート"
            options={receptionRoutes.map((route) => ({
              label: route.name,
              value: route.id,
            }))}
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <RhfSingleSelect
            control={control}
            name="receiverId"
            label="受付者"
            options={users.map((user) => ({
              label: user.name,
              value: user.id,
            }))}
            readOnly={readOnly}
            loading={isPending}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <RhfDateTimePicker
            control={control}
            name="receivedAt"
            label="受付日時"
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <RhfSingleSelect
            control={control}
            name="assignUserId"
            label="担当者"
            showEmptyValue={false}
            options={[
              { label: '未割り当て', value: '' },
              ...users.map((user) => ({
                label: user.displayName || user.name,
                value: user.id,
              })),
            ]}
            readOnly={readOnly}
            loading={isPending}
          />
        </Grid>
        <Grid size={{ xs: 12 }}>
          <RhfSimpleAddress
            control={control}
            watch={watch}
            name="address"
            label="住所"
            readOnly={readOnly}
          />
        </Grid>
        <Grid size={{ xs: 12 }}>
          <RhfTextField
            control={control}
            name="description"
            label="問い合わせ内容"
            multiline
            rows={4}
            readOnly={readOnly}
          />
        </Grid>
      </Grid>
      <RhfMultiImageSelector
        control={control}
        watch={watch}
        imagesPropName="images"
        readOnly={readOnly}
        max={5}
        newButtonLabel="写真を追加"
        limitHelperText={
          <FormHelperText sx={{ textAlign: 'center' }}>写真は最大5枚です</FormHelperText>
        }
      />
      <Grid container spacing={2}>
        <Grid size={{ xs: 12 }}>
          <RhfTextField
            control={control}
            name="memo"
            label={labels.system.memo}
            multiline
            rows={4}
            readOnly={readOnly}
          />
        </Grid>
      </Grid>
    </>
  );
};
