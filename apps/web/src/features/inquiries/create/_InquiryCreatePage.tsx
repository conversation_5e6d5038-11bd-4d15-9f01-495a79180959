import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { MainLayout } from '@/components/core/layout/MainLayout';
import { useAppContext } from '@/contexts/app-context';
import { inquiryRouters } from '@/router/routes/inquiry';
import { zodResolver } from '@hookform/resolvers/zod';
import { Add, Close } from '@mui/icons-material';
import { Alert, Paper, Stack } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import { stringOrNull } from 'common';
import { useForm } from 'react-hook-form';
import {
  type InquiryState,
  InquiryStateSchema,
  RhfInquiry,
  createDefaultInquiryState,
  generateInquiryTitle,
  uploadInquiryImagesToS3,
} from '../common/RhfInquiry';

export default function InquiryCreatePage() {
  const {
    labels,
    user,
    tenant: { receptionRoutes },
  } = useAppContext();
  const { control, watch, formState, handleSubmit } = useForm<InquiryState>({
    defaultValues: createDefaultInquiryState(user.id),
    resolver: zodResolver(InquiryStateSchema),
  });
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { mutateAsync, isError } = trpc.inquiries.create.useMutation({
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.inquiries.list) });
      navigate({ to: '/inquiries' });
    },
  });

  const title = inquiryRouters.meta.create.useTitle();

  const submit = async (state: InquiryState) => {
    const id = crypto.randomUUID();
    const images = await uploadInquiryImagesToS3(id, state.images);
    const now = new Date();
    const dynamicTitle = generateInquiryTitle(state.receptionRouteId, now, receptionRoutes);

    await mutateAsync({
      id,
      receptionRouteId: state.receptionRouteId,
      receiverId: stringOrNull(state.receiverId),
      receivedAt: state.receivedAt,
      title: dynamicTitle, // TODO: タイトルを入力できるようにする
      description: state.description,
      address: state.address,
      memo: stringOrNull(state.memo),
      images,
      assignUserId: state.assignUserId || undefined,
    });
  };
  return (
    <MainLayout scrollable back={{ to: '/inquiries' }} title={title}>
      <form noValidate onSubmit={handleSubmit(submit)}>
        <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
          <RhfInquiry control={control} watch={watch} />
          {isError && (
            <Alert severity="error">
              サーバーエラーが発生しました。管理者へお問い合わせください。
            </Alert>
          )}
          <TwoActionButtons
            primary={{
              type: 'submit',
              icon: Add,
              label: labels.doing.create,
              loading: formState.isLoading,
            }}
            secondary={{
              icon: Close,
              label: labels.doing.cancel,
              onClick: () => navigate({ to: '/inquiries' }),
              end: true,
            }}
          />
        </Stack>
      </form>
    </MainLayout>
  );
}
