import { trpc } from '@/api';
import { TwoActionButtons } from '@/components/TwoActionButtons';
import { useAppContext } from '@/contexts/app-context';
import { zodResolver } from '@hookform/resolvers/zod';
import { Close, Save } from '@mui/icons-material';
import { Alert, Paper, Stack } from '@mui/material';
import { useQueryClient } from '@tanstack/react-query';
import { useNavigate } from '@tanstack/react-router';
import { getQueryKey } from '@trpc/react-query';
import { stringOrNull } from 'common';
import type { Inquiry } from 'lambda-api';
import { useForm } from 'react-hook-form';
import {
  type InquiryState,
  InquiryStateSchema,
  RhfInquiry,
  inquiryToState,
  uploadInquiryImagesToS3,
} from '../common/RhfInquiry';

type Props = {
  inquiry: Inquiry;
};

export default function InquiryEdit({ inquiry }: Props) {
  const id = inquiry.id;
  const {
    labels,
    tenant: { receptionRoutes },
  } = useAppContext();
  const { control, watch, formState, handleSubmit } = useForm<InquiryState>({
    defaultValues: inquiryToState(inquiry, receptionRoutes),
    resolver: zodResolver(InquiryStateSchema),
  });
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { mutate, isError } = trpc.inquiries.update.useMutation({
    onSuccess: async () => {
      queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.inquiries.list) });
      queryClient.invalidateQueries({ queryKey: getQueryKey(trpc.inquiries.get, { id }) });
      navigate({ to: '/inquiries' });
    },
  });

  const submit = async (state: InquiryState) => {
    const images = await uploadInquiryImagesToS3(id, state.images);
    mutate({
      id,
      receptionRouteId: state.receptionRouteId,
      receiverId: stringOrNull(state.receiverId),
      receivedAt: state.receivedAt,
      title: state.title,
      description: state.description,
      address: state.address,
      memo: stringOrNull(state.memo),
      images,
      assignUserId: state.assignUserId || undefined,
    });
  };
  return (
    <form noValidate onSubmit={handleSubmit(submit)}>
      <Stack component={Paper} spacing={2} sx={{ p: 2 }}>
        <RhfInquiry control={control} watch={watch} />
        {isError && (
          <Alert severity="error">
            サーバーエラーが発生しました。管理者へお問い合わせください。
          </Alert>
        )}
        <TwoActionButtons
          primary={{
            type: 'submit',
            icon: Save,
            label: labels.action.save,
            loading: formState.isLoading,
          }}
          secondary={{
            icon: Close,
            label: labels.action.cancel,
            onClick: () => navigate({ to: '/inquiries' }),
            end: true,
          }}
        />
      </Stack>
    </form>
  );
}
