import type { z } from 'zod';

import { getJpFiscalYear } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import {
  analyzeAssignmentChange,
  createAssignmentUpdateData,
  handleAssignmentNotifications,
} from './assignment-utils';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const updateInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx, userId: updaterId } = ctx;
  const { id, receivedAt, assignUserId } = input;

  await asyncTx(async (tx) => {
    // Get current inquiry to check assignment changes
    const currentInquiry = await tx.inquiry.findUniqueOrThrow({
      where: { id },
      include: { assignUsers: { include: { user: true } } },
    });

    const currentAssignee = currentInquiry.assignUsers?.[0]?.user;
    const change = analyzeAssignmentChange(currentAssignee?.id, assignUserId, updaterId);

    const data: Prisma.InquiryCreateInput = {
      id,
      title: input.title,
      description: input.description,
      address: input.address,
      memo: input.memo,
      receivedAt: input.receivedAt,
      fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
      receptionRoute: { connect: { id: input.receptionRouteId } },
      receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
      images: { createMany: { data: input.images } },
      assignUsers: assignUserId ? { create: { userId: assignUserId } } : undefined,
    };

    // Update the main inquiry
    await tx.inquiry.update({
      where: { id },
      data: {
        ...data,
        assignUsers: createAssignmentUpdateData(assignUserId),
      },
    });

    // Create version
    const { id: _, ...versionData } = data; // remove id from payload, then Prisma will auto-generate new uuid
    await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });

    // Handle assignment notifications
    await handleAssignmentNotifications(tx, change, id, input.title, updaterId);
  });

  return 'OK';
};

export const updateInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(updateInquiryMutation);
