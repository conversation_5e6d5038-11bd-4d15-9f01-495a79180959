import type { z } from 'zod';

import { getJpFiscalYear } from 'common';
import type { Prisma } from '../../../../.prisma';
import type { Context, TRPCProcedure } from '../../types';
import {
  analyzeAssignmentChange,
  createAssignmentData,
  handleAssignmentNotifications,
} from './assignment-utils';
import { InquiryInputSchema } from './types';

type Input = z.infer<typeof InquiryInputSchema>;
type MutationArgs = { input: Input; ctx: Context };

export const createInquiryMutation = async ({ input, ctx }: MutationArgs) => {
  const { asyncTx, userId: creatorId } = ctx;
  const { id, receivedAt, assignUserId } = input;

  const data: Prisma.InquiryCreateInput = {
    id,
    title: input.title,
    description: input.description,
    address: input.address,
    memo: input.memo,
    receivedAt: input.receivedAt,
    fiscalYear: receivedAt ? getJpFiscalYear(receivedAt) : null,
    receptionRoute: { connect: { id: input.receptionRouteId } },
    receiver: input.receiverId ? { connect: { id: input.receiverId } } : undefined,
    images: { createMany: { data: input.images } },
    assignUsers: assignUserId ? { create: { userId: assignUserId } } : undefined,
  };

  await asyncTx(async (tx) => {
    await tx.inquiry.create({ data: { ...data, id } });
    const { id: _, ...versionData } = data; // remove id from payload, then Prisma will auto-generate new uuid
    await tx.inquiry.create({ data: { ...versionData, original: { connect: { id } } } });

    // Handle assignment notifications
    if (assignUserId) {
      const change = analyzeAssignmentChange(null, assignUserId, creatorId);
      await handleAssignmentNotifications(tx, change, id, input.title, creatorId);
    }
  });

  return 'OK';
};

export const createInquiry = (p: TRPCProcedure) =>
  p.input(InquiryInputSchema).mutation(createInquiryMutation);
