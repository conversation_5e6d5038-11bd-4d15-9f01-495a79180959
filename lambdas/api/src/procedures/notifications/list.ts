import { subDays } from 'date-fns';
import { z } from 'zod';

import type { Context, TRPCProcedure } from '../../types';

const inputSchema = z.object({ all: z.boolean().optional() }).strict().optional();
type Input = z.infer<typeof inputSchema>;

type QueryArgs = { input: Input; ctx: Context };

export const listNotificationsQuery = async ({ input, ctx }: QueryArgs) => {
  const { prisma, tenantId, userId } = ctx;
  const conditions = !input?.all
    ? {
        hide: false,
        OR: [
          { readAt: null }, // 未読
          { readAt: { gt: subDays(new Date(), 30) } }, // 既読でも30日は表示する
        ],
      }
    : {}; // all → 全て表示
  const notificationsOnUsers = await prisma.notificationsOnUsers.findMany({
    where: { tenantId, userId, ...conditions },
    include: { notification: true },
    orderBy: [
      { readAt: 'asc' }, // 未読を先に表示 (null values first)
      { notification: { createdAt: 'desc' } }, // 新しい通知を先に表示
    ],
  });
  return notificationsOnUsers.map(({ readAt, updatedAt, notification }) => ({
    ...notification,
    readAt,
    updatedAt,
  }));
};

export const listNotifications = (p: TRPCProcedure) =>
  p.input(inputSchema).query(listNotificationsQuery);
